#include <iostream>
#include <string>
#include <cstring>
#include <map>
#if defined(_WIN32)
#include <windows.h>
#endif
#include <GLFW/glfw3.h>
#include <GL/gl.h>
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include "piano_keyboard.h"
#include "opengl_renderer.h"
#include "audio_engine.h"

// Global variables for mouse input
double g_mouse_x = 0.0;
double g_mouse_y = 0.0;
bool g_mouse_is_down = false;

// Global variables for window management
PianoKeyboard* g_piano = nullptr;
OpenGLRenderer* g_renderer = nullptr;
AudioEngine* g_audio_engine = nullptr;

// Global variables for audio
AudioEngine g_audio;
bool g_audio_initialized = false;

// Global variables for UI settings
static int polyphony_setting = 13765870; // Default polyphony (extremely high for massive compositions)
static bool show_bassmidi_status = false; // Show BassMIDI status window
static bool show_midi_input = false; // Show MIDI input window
static bool show_audio_limiter = false; // Show Audio Limiter debug window
static int selected_midi_device = -1; // Selected MIDI input device
static int selected_alsa_midi_device = -1; // Selected ALSA MIDI input device
static bool use_alsa_midi = false; // Whether to use ALSA MIDI instead of BASS MIDI

// Global variables for PC keyboard input
static std::map<int, int> g_key_to_note_map; // Maps GLFW key codes to MIDI note numbers
static std::map<int, bool> g_key_pressed_state; // Tracks which keys are currently pressed

// Error callback for GLFW
void glfw_error_callback(int error, const char* description) {
    std::cerr << "GLFW Error " << error << ": " << description << std::endl;
}

// Mouse callback for GLFW
void mouse_button_callback(GLFWwindow* window, int button, int action, int mods) {
    if (button == GLFW_MOUSE_BUTTON_LEFT) {
        if (action == GLFW_PRESS) {
            g_mouse_is_down = true;
        } else if (action == GLFW_RELEASE) {
            g_mouse_is_down = false;
        }
    }
}

void cursor_position_callback(GLFWwindow* window, double xpos, double ypos) {
    g_mouse_x = xpos;
    g_mouse_y = ypos;
}

void window_size_callback(GLFWwindow* window, int width, int height) {
    if (g_piano && g_renderer) {
        g_renderer->SetViewport(width, height);
        g_piano->UpdateLayout(width, height);
    }
}

// Keyboard callback for PC keyboard input
void key_callback(GLFWwindow* window, int key, int scancode, int action, int mods) {
    // Check if this key is mapped to a note
    auto it = g_key_to_note_map.find(key);
    if (it != g_key_to_note_map.end()) {
        int note = it->second;

        if (action == GLFW_PRESS) {
            // Key pressed - only trigger if not already pressed (to avoid repeats)
            if (g_key_pressed_state[key] == false) {
                g_key_pressed_state[key] = true;
                if (g_piano) {
                    g_piano->SetKeyPressed(note, true);
                }
            }
        } else if (action == GLFW_RELEASE) {
            // Key released
            g_key_pressed_state[key] = false;
            if (g_piano) {
                g_piano->SetKeyPressed(note, false);
            }
        }
    }
}

// Initialize PC keyboard to MIDI note mapping
void initialize_keyboard_mapping() {
    // Clear existing mappings
    g_key_to_note_map.clear();
    g_key_pressed_state.clear();

    // Helper function to create note mapping
    auto map_key = [](int glfw_key, int note) {
        g_key_to_note_map[glfw_key] = note;
        g_key_pressed_state[glfw_key] = false;
    };

    // Lower row - Piano layout starting from A3 (Z = A3/ラ)
    map_key(GLFW_KEY_A, 55);      // G3 - ソ (black key position but white note)
    map_key(GLFW_KEY_Z, 57);      // A3 - ラ
    map_key(GLFW_KEY_S, 58);      // A#3/Bb3 (black key)
    map_key(GLFW_KEY_X, 59);      // B3 - シ
    map_key(GLFW_KEY_C, 60);      // C4 - ド (Middle C)
    map_key(GLFW_KEY_F, 61);      // C#4/Db4 (black key)
    map_key(GLFW_KEY_V, 62);      // D4 - レ
    map_key(GLFW_KEY_G, 63);      // D#4/Eb4 (black key)
    map_key(GLFW_KEY_B, 64);      // E4 - ミ
    map_key(GLFW_KEY_N, 65);      // F4 - ファ
    map_key(GLFW_KEY_J, 66);      // F#4/Gb4 (black key)
    map_key(GLFW_KEY_M, 67);      // G4 - ソ
    map_key(GLFW_KEY_K, 68);      // G#4/Ab4 (black key)
    map_key(GLFW_KEY_COMMA, 69);  // A4 - ラ
    map_key(GLFW_KEY_L, 70);      // A#4/Bb4 (black key)
    map_key(GLFW_KEY_PERIOD, 71); // B4 - シ
    map_key(GLFW_KEY_SEMICOLON, 72); // C5 - ド
    map_key(GLFW_KEY_SLASH, 73);  // C#5/Db5 (black key)

    // Upper row - Higher octave starting from A4 (Q = A4/ラ)
    map_key(GLFW_KEY_1, 67);      // G4 - ソ (black key position but white note)
    map_key(GLFW_KEY_Q, 69);      // A4 - ラ
    map_key(GLFW_KEY_2, 70);      // A#4/Bb4 (black key)
    map_key(GLFW_KEY_W, 71);      // B4 - シ
    map_key(GLFW_KEY_E, 72);      // C5 - ド
    map_key(GLFW_KEY_4, 73);      // C#5/Db5 (black key)
    map_key(GLFW_KEY_R, 74);      // D5 - レ
    map_key(GLFW_KEY_5, 75);      // D#5/Eb5 (black key)
    map_key(GLFW_KEY_T, 76);      // E5 - ミ
    map_key(GLFW_KEY_Y, 77);      // F5 - ファ
    map_key(GLFW_KEY_7, 78);      // F#5/Gb5 (black key)
    map_key(GLFW_KEY_U, 79);      // G5 - ソ
    map_key(GLFW_KEY_8, 80);      // G#5/Ab5 (black key)
    map_key(GLFW_KEY_I, 81);      // A5 - ラ
    map_key(GLFW_KEY_9, 82);      // A#5/Bb5 (black key)
    map_key(GLFW_KEY_O, 83);      // B5 - シ
    map_key(GLFW_KEY_P, 84);      // C6 - ド
    map_key(GLFW_KEY_MINUS, 85);  // C#6/Db6 (black key)
    map_key(GLFW_KEY_LEFT_BRACKET, 86);  // D6 - レ
    map_key(GLFW_KEY_EQUAL, 87);  // D#6/Eb6 (black key)
    map_key(GLFW_KEY_RIGHT_BRACKET, 88); // E6 - ミ
}

int main(int argc, char** argv) {
    std::cout << "Starting Piano Keyboard Application..." << std::endl;
    std::cout.flush();

    // Setup GLFW
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) {
        std::cerr << "Failed to initialize GLFW" << std::endl;
        return -1;
    }

    // GL 3.0 + GLSL 130
    const char* glsl_version = "#version 130";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 0);

    // Create window with graphics context
    GLFWwindow* window = glfwCreateWindow(1280, 720, "Piano Keyboard", NULL, NULL);
    if (window == NULL) {
        std::cerr << "Failed to create GLFW window" << std::endl;
        glfwTerminate();
        return -1;
    }
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // Enable vsync

    // Set GLFW callbacks
    glfwSetMouseButtonCallback(window, mouse_button_callback);
    glfwSetCursorPosCallback(window, cursor_position_callback);
    glfwSetWindowSizeCallback(window, window_size_callback);
    glfwSetKeyCallback(window, key_callback);

    // Initialize PC keyboard to MIDI note mapping
    initialize_keyboard_mapping();

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup Dear ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // Initialize OpenGL renderer
    OpenGLRenderer renderer;
    renderer.Initialize(1280, 720);

    // Initialize audio engine
    std::cout << "Initializing Audio Engine..." << std::endl;
    std::cout.flush();
    g_audio_initialized = g_audio.Initialize();
    if (!g_audio_initialized) {
        std::cerr << "Warning: Audio engine failed to initialize. Audio will be disabled." << std::endl;
        std::cerr.flush();
    } else {
        std::cout << "Audio engine initialized successfully!" << std::endl;
        std::cout.flush();
    }

    // Initialize piano keyboard
    PianoKeyboard piano;
    piano.Initialize(&g_audio);

    // Set global pointers for callbacks
    g_piano = &piano;
    g_renderer = &renderer;
    g_audio_engine = &g_audio;

    // Register piano keyboard with audio engine for MIDI visual feedback
    g_audio.SetPianoKeyboard(&piano);

    // Settings variables
    bool show_settings = true;
    bool show_debug = true;
    ImVec4 background_color = ImVec4(0.45f, 0.55f, 0.60f, 1.00f);
    Vec2 white_key_size = Vec2(20.0f, 120.0f);
    Vec2 black_key_size = Vec2(12.0f, 80.0f);
    bool auto_layout = true;
    float keyboard_margin = 50.0f;

    // Main loop
    while (!glfwWindowShouldClose(window)) {
        glfwPollEvents();

        // Start the Dear ImGui frame
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // Main menu bar
        if (ImGui::BeginMainMenuBar()) {
            if (ImGui::BeginMenu("View")) {
                ImGui::MenuItem("Settings", NULL, &show_settings);
                ImGui::MenuItem("Debug Info", NULL, &show_debug);
                ImGui::MenuItem("Audio Limiter", NULL, &show_audio_limiter);
                ImGui::EndMenu();
            }
            if (ImGui::BeginMenu("Piano")) {
                if (ImGui::MenuItem("Reset All Keys")) {
                    for (int i = 0; i < 128; ++i) {
                        piano.SetKeyPressed(i, false);
                    }
                }
                ImGui::EndMenu();
            }
            ImGui::EndMainMenuBar();
        }

        // Update piano keyboard
        piano.Update();

        // Process audio for all engines
        g_audio.ProcessAudio();

        // Handle mouse input for piano keyboard
        piano.HandleInput(g_mouse_x, g_mouse_y, g_mouse_is_down);

        // Get window size for debug info
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);

        // Update renderer viewport
        renderer.SetViewport(display_w, display_h);

        // Update piano keyboard layout based on window size
        piano.UpdateLayout(display_w, display_h);

        // Settings window
        if (show_settings) {
            ImGui::Begin("Settings", &show_settings);

            ImGui::Text("Keyboard Settings");
            ImGui::Separator();

            if (ImGui::Checkbox("Auto Layout", &auto_layout)) {
                piano.SetAutoLayout(auto_layout);
            }

            if (auto_layout) {
                if (ImGui::SliderFloat("Keyboard Margin", &keyboard_margin, 10.0f, 200.0f)) {
                    piano.SetKeyboardMargin(keyboard_margin);
                }
            } else {
                if (ImGui::SliderFloat2("White Key Size", &white_key_size.x, 10.0f, 50.0f)) {
                    piano.SetWhiteKeySize(white_key_size);
                }

                if (ImGui::SliderFloat2("Black Key Size", &black_key_size.x, 5.0f, 30.0f)) {
                    piano.SetBlackKeySize(black_key_size);
                }
            }

            ImGui::Text("PC Keyboard Input");
            ImGui::Separator();
            ImGui::TextWrapped("You can play notes using your PC keyboard:");
            ImGui::Text("Lower row: Z(A3) X(B3) C(C4) V(D4) B(E4) N(F4) M(G4) ,(A4)");
            ImGui::Text("Black keys: S F G J K L");
            ImGui::Text("Upper row: Q(A4) W(B4) E(C5) R(D5) T(E5) Y(F5) U(G5) I(A5)");
            ImGui::Text("Black keys: 2 4 5 7 8 9 - =");
            ImGui::TextWrapped("Z key = A3 (ラ), C key = Middle C (C4). Piano layout with white and black keys.");
            ImGui::Spacing();

            ImGui::Text("Audio Settings");
            ImGui::Separator();

            if (g_audio_initialized) {
                static bool audio_enabled = true;
                static float audio_volume = 0.8f;

                if (ImGui::Checkbox("Enable Audio", &audio_enabled)) {
                    piano.SetAudioEnabled(audio_enabled);
                }

                if (ImGui::SliderFloat("Volume", &audio_volume, 0.0f, 1.0f)) {
                    g_audio.SetVolume(audio_volume);
                }

                ImGui::Text("Soundfont Settings:");
                ImGui::Separator();

                // Show current soundfont status
                if (g_audio.IsSoundfontLoaded()) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Soundfont loaded:");
                    ImGui::Text("  %s", g_audio.GetCurrentSoundfontPath().c_str());
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "No soundfont loaded");
                    ImGui::Text("Using default MIDI sounds");
                }

                ImGui::Spacing();

                // Soundfont file input
                ImGui::Text("Load Soundfont File (.sf2):");
                static char soundfont_buffer[512] = "default.sf2";

                ImGui::PushItemWidth(-120);
                if (ImGui::InputText("##soundfont", soundfont_buffer, sizeof(soundfont_buffer))) {
                    // Update when user types
                }
                ImGui::PopItemWidth();

                ImGui::SameLine();
                if (ImGui::Button("Browse...")) {
                    // Simple file path suggestions
                    ImGui::OpenPopup("soundfont_suggestions");
                }

                // Popup with common soundfont locations
                if (ImGui::BeginPopup("soundfont_suggestions")) {
                    ImGui::Text("Common soundfont locations:");
                    ImGui::Separator();

                    if (ImGui::Selectable("./default.sf2")) {
                        strncpy(soundfont_buffer, "./default.sf2", sizeof(soundfont_buffer) - 1);
                    }
                    if (ImGui::Selectable("./soundfonts/piano.sf2")) {
                        strncpy(soundfont_buffer, "./soundfonts/piano.sf2", sizeof(soundfont_buffer) - 1);
                    }
                    if (ImGui::Selectable("./FluidR3_GM.sf2")) {
                        strncpy(soundfont_buffer, "./FluidR3_GM.sf2", sizeof(soundfont_buffer) - 1);
                    }
                    if (ImGui::Selectable("/usr/share/soundfonts/default.sf2")) {
                        strncpy(soundfont_buffer, "/usr/share/soundfonts/default.sf2", sizeof(soundfont_buffer) - 1);
                    }

                    ImGui::EndPopup();
                }

                if (ImGui::Button("Load Soundfont")) {
                    std::string soundfont_path(soundfont_buffer);
                    if (g_audio.LoadSoundfont(soundfont_path)) {
                        std::cout << "Soundfont loaded successfully" << std::endl;
                    } else {
                        std::cerr << "Failed to load soundfont: " << soundfont_path << std::endl;
                    }
                }

                ImGui::SameLine();
                if (ImGui::Button("Clear Soundfont")) {
                    // Clear the current soundfont to use default MIDI sounds
                    if (g_audio.LoadSoundfont("__clear_soundfont__")) {
                        std::cout << "Soundfont cleared, using default MIDI sounds" << std::endl;
                    }
                }

                ImGui::Spacing();
                ImGui::Separator();
                ImGui::Text("Audio Test:");

                if (ImGui::Button("Play Test Tone")) {
                    g_audio.PlayTestTone();
                }

                ImGui::SameLine();
                if (ImGui::Button("Show Devices")) {
                    g_audio.PrintDeviceInfo();
                }




                ImGui::Spacing();
                ImGui::TextWrapped("Note: Place .sf2 files in the executable directory or use full paths. Popular soundfonts include FluidR3_GM.sf2, GeneralUser_GS.sf2, or Timbres_Of_Heaven.sf2");
            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not available");
                ImGui::Text("BASS libraries may not be installed");
            }

            ImGui::Text("Display Settings");
            ImGui::Separator();

            ImGui::ColorEdit3("Background Color", (float*)&background_color);

            ImGui::Text("Windows");
            ImGui::Separator();

            ImGui::Checkbox("Show Debug Window", &show_debug);
            ImGui::Checkbox("Show BassMIDI Status", &show_bassmidi_status);
            ImGui::Checkbox("Show MIDI Input", &show_midi_input);

            if (ImGui::Button("Reset All Keys")) {
                for (int i = 0; i < 128; ++i) {
                    piano.SetKeyPressed(i, false);
                }
            }

            ImGui::End();
        }

        // Piano keyboard is now rendered directly with OpenGL (see below)

        // Debug window
        if (show_debug) {
            ImGui::Begin("Debug Info", &show_debug);
            ImGui::Text("Application average %.3f ms/frame (%.1f FPS)",
                       1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
            ImGui::Text("Pressed keys: %d", piano.GetPressedKeyCount());

            auto pressed_keys = piano.GetPressedKeys();
            if (!pressed_keys.empty()) {
                ImGui::Text("Active notes:");
                for (int note : pressed_keys) {
                    ImGui::SameLine();
                    ImGui::Text("%d", note);
                }
            }

            ImGui::Text("Mouse Position: (%.1f, %.1f)", ImGui::GetIO().MousePos.x, ImGui::GetIO().MousePos.y);
            ImGui::Text("Window Size: (%d, %d)", display_w, display_h);

            ImGui::Separator();
            ImGui::Text("Audio Engine: %s", g_audio.IsInitialized() ? "Initialized" : "Not Available");
            if (g_audio.IsInitialized()) {
                ImGui::Text("Audio Enabled: %s", piano.IsAudioEnabled() ? "Yes" : "No");
                ImGui::Text("Volume: %.2f", g_audio.GetVolume());
                ImGui::Text("Soundfont: %s", g_audio.IsSoundfontLoaded() ?
                    g_audio.GetCurrentSoundfontPath().c_str() : "None (using default sounds)");
                ImGui::Text("Polyphony: %d / %d", g_audio.GetCurrentPolyphony(), g_audio.GetMaxPolyphony());

                // Polyphony bar
                float polyphony_ratio = g_audio.GetMaxPolyphony() > 0 ?
                    (float)g_audio.GetCurrentPolyphony() / (float)g_audio.GetMaxPolyphony() : 0.0f;
                ImGui::ProgressBar(polyphony_ratio, ImVec2(-1, 0),
                    (std::to_string(g_audio.GetCurrentPolyphony()) + " / " + std::to_string(g_audio.GetMaxPolyphony())).c_str());

                // Polyphony setting
                ImGui::Spacing();
                ImGui::Text("Polyphony Settings:");

                // Initialize polyphony_setting with current value if not set
                if (polyphony_setting != g_audio.GetMaxPolyphony()) {
                    polyphony_setting = g_audio.GetMaxPolyphony();
                }

                // Use a slider for common values (1-1000) and input field for higher values
                if (ImGui::SliderInt("Max Polyphony", &polyphony_setting, 1, 1000)) {
                    if (g_audio.SetMaxPolyphony(polyphony_setting)) {
                        std::cout << "Polyphony changed to: " << polyphony_setting << std::endl;
                    }
                }

                // Input field for custom values up to 13,765,870
                ImGui::Text("Custom Value (1-13765870):");
                if (ImGui::InputInt("##polyphony_input", &polyphony_setting, 1, 1000)) {
                    // Clamp the value to valid range
                    if (polyphony_setting < 1) polyphony_setting = 1;
                    if (polyphony_setting > 13765870) polyphony_setting = 13765870;

                    if (g_audio.SetMaxPolyphony(polyphony_setting)) {
                        std::cout << "Polyphony changed to: " << polyphony_setting << std::endl;
                    }
                }

                // Preset buttons for common polyphony values
                ImGui::Text("Presets:");
                if (ImGui::Button("16")) {
                    polyphony_setting = 16;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
                ImGui::SameLine();
                if (ImGui::Button("32")) {
                    polyphony_setting = 32;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
                ImGui::SameLine();
                if (ImGui::Button("64")) {
                    polyphony_setting = 64;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
                ImGui::SameLine();
                if (ImGui::Button("128")) {
                    polyphony_setting = 128;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
                ImGui::SameLine();
                if (ImGui::Button("256")) {
                    polyphony_setting = 256;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }

                // Second row of presets
                if (ImGui::Button("512")) {
                    polyphony_setting = 512;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
                ImGui::SameLine();
                if (ImGui::Button("1000")) {
                    polyphony_setting = 1000;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
                ImGui::SameLine();
                if (ImGui::Button("10000")) {
                    polyphony_setting = 10000;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }

                // Third row of presets
                if (ImGui::Button("50000")) {
                    polyphony_setting = 50000;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
                ImGui::SameLine();
                if (ImGui::Button("500000")) {
                    polyphony_setting = 500000;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
                ImGui::SameLine();
                if (ImGui::Button("1000000")) {
                    polyphony_setting = 1000000;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }

                // Fourth row of presets
                if (ImGui::Button("5000000")) {
                    polyphony_setting = 5000000;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
                ImGui::SameLine();
                if (ImGui::Button("13765870 (Max)")) {
                    polyphony_setting = 13765870;
                    g_audio.SetMaxPolyphony(polyphony_setting);
                }
            }

            ImGui::End();
        }

        // BassMIDI Status Window
        if (show_bassmidi_status) {
            ImGui::Begin("BassMIDI Status", &show_bassmidi_status);

            if (g_audio.IsInitialized()) {
                // Performance metrics
                ImGui::Text("Performance Metrics");
                ImGui::Separator();

                float cpu_usage = g_audio.GetCPUUsage();
                ImGui::Text("CPU Usage: %.2f%%", cpu_usage);
                ImGui::ProgressBar(cpu_usage / 100.0f, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(cpu_usage)) + "%").c_str());

                float render_time = g_audio.GetRenderingTime();
                ImGui::Text("Rendering Time: %.3f ms", render_time);

                // Voice and channel information
                ImGui::Spacing();
                ImGui::Text("Voice Information");
                ImGui::Separator();

                int current_voices = g_audio.GetCurrentPolyphony();
                int max_voices = g_audio.GetMaxPolyphony();
                ImGui::Text("Active Voices: %d / %d", current_voices, max_voices);

                float voice_ratio = max_voices > 0 ? (float)current_voices / (float)max_voices : 0.0f;
                ImGui::ProgressBar(voice_ratio, ImVec2(-1, 0),
                    (std::to_string(current_voices) + " / " + std::to_string(max_voices)).c_str());

                int active_channels = g_audio.GetActiveChannels();
                ImGui::Text("Active Channels: %d", active_channels);

                // Audio system information
                ImGui::Spacing();
                ImGui::Text("Audio System Information");
                ImGui::Separator();

                std::string audio_info = g_audio.GetAudioInfo();
                ImGui::TextWrapped("%s", audio_info.c_str());

                // Real-time statistics
                ImGui::Spacing();
                ImGui::Text("Real-time Statistics");
                ImGui::Separator();

                static float cpu_history[100] = {0};
                static int cpu_history_offset = 0;
                cpu_history[cpu_history_offset] = cpu_usage;
                cpu_history_offset = (cpu_history_offset + 1) % 100;

                ImGui::PlotLines("CPU Usage History", cpu_history, 100, cpu_history_offset,
                    "CPU %", 0.0f, 100.0f, ImVec2(0, 80));

                static float voice_history[100] = {0};
                static int voice_history_offset = 0;
                voice_history[voice_history_offset] = voice_ratio * 100.0f;
                voice_history_offset = (voice_history_offset + 1) % 100;

                ImGui::PlotLines("Voice Usage History", voice_history, 100, voice_history_offset,
                    "Voices %", 0.0f, 100.0f, ImVec2(0, 80));

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not initialized");
                ImGui::Text("BassMIDI status information is not available");
            }

            ImGui::End();
        }

        // MIDI Input Window
        if (show_midi_input) {
            ImGui::Begin("MIDI Input", &show_midi_input);

            if (g_audio.IsInitialized()) {
                // MIDI Input Type Selection
                ImGui::Text("MIDI Input Type");
                ImGui::Separator();

                if (ImGui::RadioButton("BASS MIDI Input", !use_alsa_midi)) {
                    if (use_alsa_midi) {
                        // Switch from ALSA to BASS
                        g_audio.CloseALSAMIDIDevice();
                        selected_alsa_midi_device = -1;
                        use_alsa_midi = false;
                    }
                }
                ImGui::SameLine();
                if (ImGui::RadioButton("ALSA MIDI Input", use_alsa_midi)) {
                    if (!use_alsa_midi) {
                        // Switch from BASS to ALSA
                        g_audio.CloseMIDIInputDevice();
                        selected_midi_device = -1;
                        use_alsa_midi = true;
                    }
                }

                ImGui::Spacing();
                ImGui::Separator();

                if (!use_alsa_midi) {
                    // BASS MIDI Input Device Selection
                    ImGui::Text("BASS MIDI Input Devices");
                    ImGui::Separator();

                    std::vector<std::string> midi_devices = g_audio.GetMIDIInputDevices();

                    if (midi_devices.empty()) {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "No BASS MIDI input devices found");
                        ImGui::Text("Make sure MIDI devices are connected and drivers are installed");
                    } else {
                        ImGui::Text("Available BASS MIDI Input Devices:");

                        for (size_t i = 0; i < midi_devices.size(); i++) {
                            bool is_selected = (selected_midi_device == static_cast<int>(i));

                            // Create unique ID for each selectable item
                            std::string device_label = midi_devices[i] + "##bass_midi_device_" + std::to_string(i);

                            if (ImGui::Selectable(device_label.c_str(), is_selected)) {
                                if (selected_midi_device != static_cast<int>(i)) {
                                    selected_midi_device = static_cast<int>(i);
                                    if (g_audio.OpenMIDIInputDevice(selected_midi_device)) {
                                        std::cout << "Opened BASS MIDI input device: " << midi_devices[i] << std::endl;
                                    } else {
                                        std::cerr << "Failed to open BASS MIDI input device: " << midi_devices[i] << std::endl;
                                        selected_midi_device = -1;
                                    }
                                }
                            }

                            if (is_selected) {
                                ImGui::SetItemDefaultFocus();
                            }
                        }
                    }
                } else {
                    // ALSA MIDI Input Device Selection
                    ImGui::Text("ALSA MIDI Input Devices");
                    ImGui::Separator();

                    std::vector<ALSAMIDIDevice> alsa_devices = g_audio.GetALSAMIDIDevices();

                    if (alsa_devices.empty()) {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "No ALSA MIDI input devices found");
                        ImGui::Text("Make sure MIDI devices are connected and ALSA is properly configured");
                    } else {
                        ImGui::Text("Available ALSA MIDI Input Devices:");

                        for (size_t i = 0; i < alsa_devices.size(); i++) {
                            bool is_selected = (selected_alsa_midi_device == static_cast<int>(i));

                            // Create unique ID for each selectable item
                            std::string device_label = alsa_devices[i].full_name + "##alsa_midi_device_" + std::to_string(i);

                            if (ImGui::Selectable(device_label.c_str(), is_selected)) {
                                if (selected_alsa_midi_device != static_cast<int>(i)) {
                                    selected_alsa_midi_device = static_cast<int>(i);
                                    if (g_audio.OpenALSAMIDIDevice(alsa_devices[i].client_id, alsa_devices[i].port_id)) {
                                        std::cout << "Opened ALSA MIDI input device: " << alsa_devices[i].full_name << std::endl;
                                    } else {
                                        std::cerr << "Failed to open ALSA MIDI input device: " << alsa_devices[i].full_name << std::endl;
                                        selected_alsa_midi_device = -1;
                                    }
                                }
                            }

                            if (is_selected) {
                                ImGui::SetItemDefaultFocus();
                            }
                        }
                    }
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Current MIDI Input Status
                ImGui::Text("MIDI Input Status");
                ImGui::Separator();

                if (!use_alsa_midi) {
                    // BASS MIDI Status
                    if (g_audio.IsMIDIInputOpen()) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "BASS MIDI Input: ACTIVE");
                        ImGui::Text("Device: %s", g_audio.GetCurrentMIDIInputDevice().c_str());

                        if (ImGui::Button("Close BASS MIDI Input")) {
                            g_audio.CloseMIDIInputDevice();
                            selected_midi_device = -1;
                        }
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "BASS MIDI Input: INACTIVE");
                        ImGui::Text("No BASS MIDI input device is currently open");
                    }
                } else {
                    // ALSA MIDI Status
                    if (g_audio.IsALSAMIDIOpen()) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "ALSA MIDI Input: ACTIVE");
                        ALSAMIDIDevice current_device = g_audio.GetCurrentALSAMIDIDevice();
                        ImGui::Text("Device: %s", current_device.full_name.c_str());

                        if (ImGui::Button("Close ALSA MIDI Input")) {
                            g_audio.CloseALSAMIDIDevice();
                            selected_alsa_midi_device = -1;
                        }
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "ALSA MIDI Input: INACTIVE");
                        ImGui::Text("No ALSA MIDI input device is currently open");
                    }
                }

                ImGui::Spacing();
                ImGui::Separator();

                // MIDI Input Instructions
                ImGui::Text("Instructions");
                ImGui::Separator();
                ImGui::TextWrapped("1. Connect your MIDI keyboard or controller");
                ImGui::TextWrapped("2. Choose between BASS MIDI Input (Windows-compatible) or ALSA MIDI Input (Linux native)");
                ImGui::TextWrapped("3. Select a MIDI input device from the list above");
                ImGui::TextWrapped("4. Play notes on your MIDI device to hear them through the piano");
                ImGui::TextWrapped("5. MIDI Note On/Off messages will be automatically converted to piano notes");
                ImGui::Spacing();
                ImGui::TextWrapped("Note: ALSA MIDI Input provides better Linux compatibility and lower latency for native ALSA devices.");

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not initialized");
                ImGui::Text("MIDI input requires the audio engine to be running");
            }

            ImGui::End();
        }

        // Audio Limiter Debug Window
        if (show_audio_limiter) {
            ImGui::Begin("Audio Limiter", &show_audio_limiter);

            if (g_audio.IsInitialized()) {
                AudioLimiter* limiter = g_audio.GetAudioLimiter();

                // Enable/Disable Limiter
                ImGui::Text("Audio Limiter Control");
                ImGui::Separator();

                bool limiter_enabled = g_audio.IsLimiterEnabled();
                if (ImGui::Checkbox("Enable Audio Limiter", &limiter_enabled)) {
                    g_audio.SetLimiterEnabled(limiter_enabled);
                }

                if (limiter_enabled) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Status: ACTIVE");
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Status: DISABLED");
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Limiter Parameters
                ImGui::Text("Limiter Parameters");
                ImGui::Separator();

                // Threshold
                float threshold = limiter->GetThreshold();
                if (ImGui::SliderFloat("Threshold (dB)", &threshold, -60.0f, 0.0f, "%.1f dB")) {
                    limiter->SetThreshold(threshold);
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##threshold")) {
                    limiter->SetThreshold(-6.0f);
                }

                // Ratio
                float ratio = limiter->GetRatio();
                if (ImGui::SliderFloat("Ratio", &ratio, 1.0f, 20.0f, "%.1f:1")) {
                    limiter->SetRatio(ratio);
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##ratio")) {
                    limiter->SetRatio(10.0f);
                }

                // Attack Time
                float attack = limiter->GetAttackTime();
                if (ImGui::SliderFloat("Attack (ms)", &attack, 0.1f, 100.0f, "%.1f ms")) {
                    limiter->SetAttackTime(attack);
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##attack")) {
                    limiter->SetAttackTime(1.0f);
                }

                // Release Time
                float release = limiter->GetReleaseTime();
                if (ImGui::SliderFloat("Release (ms)", &release, 10.0f, 5000.0f, "%.0f ms")) {
                    limiter->SetReleaseTime(release);
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##release")) {
                    limiter->SetReleaseTime(100.0f);
                }

                // Look-ahead Time
                float lookahead = limiter->GetLookAheadTime();
                if (ImGui::SliderFloat("Look-ahead (ms)", &lookahead, 0.0f, 10.0f, "%.1f ms")) {
                    limiter->SetLookAheadTime(lookahead);
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##lookahead")) {
                    limiter->SetLookAheadTime(5.0f);
                }

                // Makeup Gain
                float makeup_gain = limiter->GetMakeupGain();
                if (ImGui::SliderFloat("Makeup Gain (dB)", &makeup_gain, -20.0f, 20.0f, "%.1f dB")) {
                    limiter->SetMakeupGain(makeup_gain);
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##makeup")) {
                    limiter->SetMakeupGain(0.0f);
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Real-time Monitoring
                ImGui::Text("Real-time Monitoring");
                ImGui::Separator();

                // Gain Reduction Meter
                float gain_reduction = limiter->GetGainReduction();
                ImGui::Text("Gain Reduction: %.1f dB", gain_reduction);
                float gr_normalized = std::max(0.0f, -gain_reduction / 20.0f); // Normalize to 0-1
                ImGui::ProgressBar(gr_normalized, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(gain_reduction)) + " dB").c_str());

                // Peak Level Meter
                float peak_level = limiter->GetPeakLevel();
                ImGui::Text("Input Peak: %.1f dB", peak_level);
                float peak_normalized = std::max(0.0f, (peak_level + 60.0f) / 60.0f); // -60dB to 0dB
                ImVec4 peak_color = peak_level > -3.0f ? ImVec4(1.0f, 0.0f, 0.0f, 1.0f) :
                                   peak_level > -12.0f ? ImVec4(1.0f, 1.0f, 0.0f, 1.0f) :
                                   ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
                ImGui::PushStyleColor(ImGuiCol_PlotHistogram, peak_color);
                ImGui::ProgressBar(peak_normalized, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(peak_level)) + " dB").c_str());
                ImGui::PopStyleColor();

                // Output Level Meter
                float output_level = limiter->GetOutputLevel();
                ImGui::Text("Output Level: %.1f dB", output_level);
                float output_normalized = std::max(0.0f, (output_level + 60.0f) / 60.0f); // -60dB to 0dB
                ImVec4 output_color = output_level > -3.0f ? ImVec4(1.0f, 0.0f, 0.0f, 1.0f) :
                                     output_level > -12.0f ? ImVec4(1.0f, 1.0f, 0.0f, 1.0f) :
                                     ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
                ImGui::PushStyleColor(ImGuiCol_PlotHistogram, output_color);
                ImGui::ProgressBar(output_normalized, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(output_level)) + " dB").c_str());
                ImGui::PopStyleColor();

                ImGui::Spacing();
                ImGui::Separator();

                // Preset Buttons
                ImGui::Text("Presets");
                ImGui::Separator();

                if (ImGui::Button("Gentle Limiter")) {
                    limiter->SetThreshold(-12.0f);
                    limiter->SetRatio(4.0f);
                    limiter->SetAttackTime(5.0f);
                    limiter->SetReleaseTime(200.0f);
                    limiter->SetMakeupGain(3.0f);
                }
                ImGui::SameLine();
                if (ImGui::Button("Hard Limiter")) {
                    limiter->SetThreshold(-3.0f);
                    limiter->SetRatio(20.0f);
                    limiter->SetAttackTime(0.5f);
                    limiter->SetReleaseTime(50.0f);
                    limiter->SetMakeupGain(0.0f);
                }
                ImGui::SameLine();
                if (ImGui::Button("Transparent")) {
                    limiter->SetThreshold(-6.0f);
                    limiter->SetRatio(8.0f);
                    limiter->SetAttackTime(2.0f);
                    limiter->SetReleaseTime(100.0f);
                    limiter->SetMakeupGain(2.0f);
                }

                if (ImGui::Button("Reset All")) {
                    limiter->Reset();
                    limiter->SetThreshold(-6.0f);
                    limiter->SetRatio(10.0f);
                    limiter->SetAttackTime(1.0f);
                    limiter->SetReleaseTime(100.0f);
                    limiter->SetLookAheadTime(5.0f);
                    limiter->SetMakeupGain(0.0f);
                }

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not initialized");
                ImGui::Text("Audio Limiter requires the audio engine to be running");
            }

            ImGui::End();
        }

        // Rendering
        ImGui::Render();

        // Clear screen with background color
        Color bg_color(background_color.x, background_color.y, background_color.z, background_color.w);
        renderer.Clear(bg_color);

        // Render piano keyboard with OpenGL
        piano.Render(renderer);

        // Render ImGui on top
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

        glfwSwapBuffers(window);
    }

    // Cleanup
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}
