#include "audio_limiter.h"
#include <iostream>
#include <cstring>

AudioLimiter::AudioLimiter()
    : sample_rate_(44100.0f)
    , channels_(2)
    , enabled_(true)
    , threshold_db_(-6.0f)
    , ratio_(10.0f)
    , attack_ms_(1.0f)
    , release_ms_(100.0f)
    , lookahead_ms_(5.0f)
    , makeup_gain_db_(0.0f)
    , threshold_linear_(0.5f)
    , ratio_inv_(0.1f)
    , attack_coeff_(0.0f)
    , release_coeff_(0.0f)
    , makeup_gain_linear_(1.0f)
    , envelope_(0.0f)
    , gain_reduction_db_(0.0f)
    , peak_level_db_(-60.0f)
    , output_level_db_(-60.0f)
    , lookahead_samples_(0)
    , buffer_write_pos_(0)
    , buffer_read_pos_(0)
    , peak_buffer_size_(1024)
    , peak_write_pos_(0)
{
    peak_buffer_.resize(peak_buffer_size_, 0.0f);
}

AudioLimiter::~AudioLimiter() {
}

void AudioLimiter::Initialize(float sample_rate, int channels) {
    sample_rate_ = sample_rate;
    channels_ = channels;
    
    // Calculate look-ahead buffer size
    lookahead_samples_ = static_cast<int>(lookahead_ms_ * sample_rate_ / 1000.0f);
    lookahead_buffer_.resize(lookahead_samples_ * channels_, 0.0f);
    
    buffer_write_pos_ = 0;
    buffer_read_pos_ = 0;
    
    UpdateCoefficients();
    Reset();
    
    std::cout << "AudioLimiter initialized: " << sample_rate_ << "Hz, " 
              << channels_ << " channels, " << lookahead_samples_ << " lookahead samples" << std::endl;
}

void AudioLimiter::ProcessSamples(float* samples, int sample_count) {
    if (!enabled_ || sample_count <= 0) {
        return;
    }
    
    for (int i = 0; i < sample_count; ++i) {
        // Process each channel
        for (int ch = 0; ch < channels_; ++ch) {
            int sample_idx = i * channels_ + ch;
            float input_sample = samples[sample_idx];
            
            // Update peak level monitoring
            UpdatePeakLevel(std::abs(input_sample));
            
            // Look-ahead processing
            if (lookahead_samples_ > 0) {
                // Store current sample in look-ahead buffer
                int buffer_idx = buffer_write_pos_ * channels_ + ch;
                lookahead_buffer_[buffer_idx] = input_sample;
                
                // Get delayed sample from buffer
                int read_idx = buffer_read_pos_ * channels_ + ch;
                input_sample = lookahead_buffer_[read_idx];
            }
            
            // Compute gain reduction based on peak level
            float input_level = std::abs(input_sample);
            float gain_reduction = ComputeGainReduction(input_level);
            
            // Apply envelope follower
            if (gain_reduction < envelope_) {
                // Attack
                envelope_ = gain_reduction + (envelope_ - gain_reduction) * attack_coeff_;
            } else {
                // Release
                envelope_ = gain_reduction + (envelope_ - gain_reduction) * release_coeff_;
            }
            
            // Apply gain reduction and makeup gain
            float output_sample = input_sample * envelope_ * makeup_gain_linear_;
            samples[sample_idx] = output_sample;
            
            // Update output level monitoring
            UpdateOutputLevel(std::abs(output_sample));
        }
        
        // Update buffer positions for look-ahead
        if (lookahead_samples_ > 0) {
            buffer_write_pos_ = (buffer_write_pos_ + 1) % lookahead_samples_;
            buffer_read_pos_ = (buffer_read_pos_ + 1) % lookahead_samples_;
        }
    }
    
    // Update gain reduction display value
    gain_reduction_db_ = LinearToDb(envelope_);
}

void AudioLimiter::SetThreshold(float threshold_db) {
    threshold_db_ = std::max(-60.0f, std::min(0.0f, threshold_db));
    UpdateCoefficients();
}

void AudioLimiter::SetRatio(float ratio) {
    ratio_ = std::max(1.0f, std::min(20.0f, ratio));
    UpdateCoefficients();
}

void AudioLimiter::SetAttackTime(float attack_ms) {
    attack_ms_ = std::max(0.1f, std::min(100.0f, attack_ms));
    UpdateCoefficients();
}

void AudioLimiter::SetReleaseTime(float release_ms) {
    release_ms_ = std::max(10.0f, std::min(5000.0f, release_ms));
    UpdateCoefficients();
}

void AudioLimiter::SetLookAheadTime(float lookahead_ms) {
    lookahead_ms_ = std::max(0.0f, std::min(10.0f, lookahead_ms));
    
    // Reinitialize if sample rate is set
    if (sample_rate_ > 0) {
        Initialize(sample_rate_, channels_);
    }
}

void AudioLimiter::SetMakeupGain(float gain_db) {
    makeup_gain_db_ = std::max(-20.0f, std::min(20.0f, gain_db));
    UpdateCoefficients();
}

void AudioLimiter::Reset() {
    envelope_ = 1.0f;
    gain_reduction_db_ = 0.0f;
    peak_level_db_ = -60.0f;
    output_level_db_ = -60.0f;
    buffer_write_pos_ = 0;
    buffer_read_pos_ = 0;
    peak_write_pos_ = 0;
    
    // Clear buffers
    std::fill(lookahead_buffer_.begin(), lookahead_buffer_.end(), 0.0f);
    std::fill(peak_buffer_.begin(), peak_buffer_.end(), 0.0f);
}

void AudioLimiter::UpdateCoefficients() {
    threshold_linear_ = DbToLinear(threshold_db_);
    ratio_inv_ = 1.0f / ratio_;
    makeup_gain_linear_ = DbToLinear(makeup_gain_db_);
    
    // Calculate envelope coefficients
    if (sample_rate_ > 0) {
        attack_coeff_ = std::exp(-1.0f / (attack_ms_ * sample_rate_ / 1000.0f));
        release_coeff_ = std::exp(-1.0f / (release_ms_ * sample_rate_ / 1000.0f));
    }
}

float AudioLimiter::LinearToDb(float linear) const {
    if (linear <= 0.0f) return -60.0f;
    return 20.0f * std::log10(linear);
}

float AudioLimiter::DbToLinear(float db) const {
    return std::pow(10.0f, db / 20.0f);
}

float AudioLimiter::ComputeGainReduction(float input_level) {
    if (input_level <= threshold_linear_) {
        return 1.0f; // No gain reduction
    }
    
    // Calculate gain reduction using compression ratio
    float over_threshold_db = LinearToDb(input_level) - threshold_db_;
    float compressed_db = over_threshold_db * ratio_inv_;
    float gain_reduction_db = over_threshold_db - compressed_db;
    
    return DbToLinear(-gain_reduction_db);
}

void AudioLimiter::UpdatePeakLevel(float sample) {
    // Store sample in peak buffer
    peak_buffer_[peak_write_pos_] = sample;
    peak_write_pos_ = (peak_write_pos_ + 1) % peak_buffer_size_;
    
    // Find peak in buffer
    float peak = 0.0f;
    for (float val : peak_buffer_) {
        peak = std::max(peak, val);
    }
    
    peak_level_db_ = LinearToDb(peak);
}

void AudioLimiter::UpdateOutputLevel(float sample) {
    // Simple peak hold for output level
    static float output_peak = 0.0f;
    static int hold_counter = 0;
    
    output_peak = std::max(output_peak, sample);
    
    if (++hold_counter >= static_cast<int>(sample_rate_ * 0.1f)) { // 100ms hold
        output_level_db_ = LinearToDb(output_peak);
        output_peak *= 0.9f; // Slow decay
        hold_counter = 0;
    }
}
