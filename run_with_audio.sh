#!/bin/bash

# Ensure PulseAudio compatibility for BASS
export PULSE_RUNTIME_PATH="/run/user/$(id -u)/pulse"
export ALSA_PCM_CARD=0
export ALSA_PCM_DEVICE=0

# Check if PulseAudio is running
if ! pulseaudio --check; then
    echo "PulseAudio is not running, starting it..."
    pulseaudio --start --daemonize
fi

# Run the application
echo "Starting Piano application with audio support..."
./build/linux/x86_64/release/pianowo
