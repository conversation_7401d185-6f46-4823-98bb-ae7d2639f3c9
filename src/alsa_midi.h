#pragma once

#include <string>
#include <vector>
#include <functional>
#include <thread>
#include <atomic>
#include <memory>

#ifdef __linux__
#include <alsa/asoundlib.h>
#endif

// MIDI message structure
struct MIDIMessage {
    unsigned char status;
    unsigned char data1;
    unsigned char data2;
    double timestamp;
};

// MIDI device information
struct ALSAMIDIDevice {
    int client_id;
    int port_id;
    std::string name;
    std::string full_name;
    bool is_input;
    bool is_output;
};

// Callback function type for ALSA MIDI input
using ALSAMIDIInputCallback = std::function<void(const MIDIMessage& message)>;

class ALSAMIDIInput {
public:
    ALSAMIDIInput();
    ~ALSAMIDIInput();

    // Initialize ALSA MIDI system
    bool Initialize();
    
    // Cleanup ALSA MIDI system
    void Cleanup();
    
    // Get list of available MIDI input devices
    std::vector<ALSAMIDIDevice> GetInputDevices() const;
    
    // Open a MIDI input device
    bool OpenDevice(int client_id, int port_id);
    
    // Close current MIDI input device
    void CloseDevice();
    
    // Check if a device is currently open
    bool IsDeviceOpen() const;
    
    // Get current device information
    ALSAMIDIDevice GetCurrentDevice() const;
    
    // Set callback for MIDI input messages
    void SetMIDICallback(ALSAMIDIInputCallback callback);
    
    // Start/stop MIDI input processing
    bool StartInput();
    void StopInput();
    
    // Check if input is currently active
    bool IsInputActive() const;

private:
#ifdef __linux__
    snd_seq_t* seq_handle_;
    int client_id_;
    int port_id_;
    int queue_id_;
    
    ALSAMIDIDevice current_device_;
    ALSAMIDIInputCallback midi_callback_;
    
    std::atomic<bool> input_active_;
    std::atomic<bool> should_stop_;
    std::unique_ptr<std::thread> input_thread_;
    
    // Internal methods
    void InputThreadFunction();
    void ProcessMIDIEvent(const snd_seq_event_t* event);
    MIDIMessage ConvertToMIDIMessage(const snd_seq_event_t* event);
    std::string GetClientName(int client_id) const;
    std::string GetPortName(int client_id, int port_id) const;
#endif
    
    bool initialized_;
};
