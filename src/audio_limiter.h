#pragma once

#include <vector>
#include <cmath>
#include <algorithm>

/**
 * Real-time Audio Limiter
 * 
 * A digital audio limiter that prevents audio signals from exceeding a specified threshold.
 * Features:
 * - Configurable threshold, ratio, attack, and release times
 * - Look-ahead processing for transparent limiting
 * - Real-time gain reduction monitoring
 * - Multi-channel support
 */
class AudioLimiter {
public:
    AudioLimiter();
    ~AudioLimiter();

    // Initialize the limiter with sample rate and channel count
    void Initialize(float sample_rate, int channels = 2);
    
    // Process audio samples (interleaved format)
    void ProcessSamples(float* samples, int sample_count);
    
    // Parameter setters
    void SetThreshold(float threshold_db);      // Threshold in dB (-60.0 to 0.0)
    void SetRatio(float ratio);                 // Compression ratio (1.0 to 20.0)
    void SetAttackTime(float attack_ms);        // Attack time in milliseconds (0.1 to 100.0)
    void SetReleaseTime(float release_ms);      // Release time in milliseconds (10.0 to 5000.0)
    void SetLookAheadTime(float lookahead_ms);  // Look-ahead time in milliseconds (0.0 to 10.0)
    void SetMakeupGain(float gain_db);          // Makeup gain in dB (-20.0 to 20.0)
    
    // Parameter getters
    float GetThreshold() const { return threshold_db_; }
    float GetRatio() const { return ratio_; }
    float GetAttackTime() const { return attack_ms_; }
    float GetReleaseTime() const { return release_ms_; }
    float GetLookAheadTime() const { return lookahead_ms_; }
    float GetMakeupGain() const { return makeup_gain_db_; }
    
    // Monitoring
    float GetGainReduction() const { return gain_reduction_db_; }  // Current gain reduction in dB
    float GetPeakLevel() const { return peak_level_db_; }          // Current peak level in dB
    float GetOutputLevel() const { return output_level_db_; }      // Current output level in dB
    
    // Enable/disable limiter
    void SetEnabled(bool enabled) { enabled_ = enabled; }
    bool IsEnabled() const { return enabled_; }
    
    // Reset internal state
    void Reset();

private:
    // Configuration
    float sample_rate_;
    int channels_;
    bool enabled_;
    
    // Parameters
    float threshold_db_;
    float ratio_;
    float attack_ms_;
    float release_ms_;
    float lookahead_ms_;
    float makeup_gain_db_;
    
    // Internal processing variables
    float threshold_linear_;
    float ratio_inv_;
    float attack_coeff_;
    float release_coeff_;
    float makeup_gain_linear_;
    
    // State variables
    float envelope_;
    float gain_reduction_db_;
    float peak_level_db_;
    float output_level_db_;
    
    // Look-ahead buffer
    std::vector<float> lookahead_buffer_;
    int lookahead_samples_;
    int buffer_write_pos_;
    int buffer_read_pos_;
    
    // Peak detection
    std::vector<float> peak_buffer_;
    int peak_buffer_size_;
    int peak_write_pos_;
    
    // Helper functions
    void UpdateCoefficients();
    float LinearToDb(float linear) const;
    float DbToLinear(float db) const;
    float ComputeGainReduction(float input_level);
    void UpdatePeakLevel(float sample);
    void UpdateOutputLevel(float sample);
};
