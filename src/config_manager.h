#pragma once

#include <string>
#include <memory>

// Configuration structure to hold all settings
struct AppConfig {
    // Keyboard settings
    struct KeyboardSettings {
        bool auto_layout = true;
        float keyboard_margin = 50.0f;
        float white_key_width = 20.0f;
        float white_key_height = 120.0f;
        float black_key_width = 12.0f;
        float black_key_height = 80.0f;
    } keyboard;

    // Audio settings
    struct AudioSettings {
        bool audio_enabled = true;
        float volume = 0.8f;
        std::string soundfont_path = "default.sf2";
        int polyphony = 13765870;
        bool limiter_enabled = false;

        // Audio Limiter settings
        float limiter_threshold = -6.0f;      // dB
        float limiter_ratio = 10.0f;          // ratio
        float limiter_attack_time = 1.0f;     // ms
        float limiter_release_time = 100.0f;  // ms
        float limiter_lookahead_time = 5.0f;  // ms
        float limiter_makeup_gain = 0.0f;     // dB
    } audio;

    // Display settings
    struct DisplaySettings {
        float background_color[3] = {0.45f, 0.55f, 0.60f}; // RGB
        bool show_settings = true;
        bool show_debug = true;
        bool show_bassmidi_status = false;
        bool show_midi_input = false;
        bool show_audio_limiter = false;
    } display;

    // MIDI settings
    struct MIDISettings {
        int selected_midi_device = -1;
        int selected_alsa_midi_device = -1;
        bool use_alsa_midi = false;
    } midi;

    // Window settings
    struct WindowSettings {
        int width = 1200;
        int height = 800;
        bool maximized = false;
    } window;
};

class ConfigManager {
public:
    ConfigManager();
    ~ConfigManager();

    // Initialize the config manager with config file path
    bool Initialize(const std::string& config_file_path = "");

    // Load configuration from file
    bool LoadConfig();

    // Save configuration to file
    bool SaveConfig();

    // Auto-save configuration (called when settings change)
    void AutoSave();

    // Get configuration reference
    AppConfig& GetConfig() { return config_; }
    const AppConfig& GetConfig() const { return config_; }

    // Get config file path
    const std::string& GetConfigFilePath() const { return config_file_path_; }

    // Check if auto-save is enabled
    bool IsAutoSaveEnabled() const { return auto_save_enabled_; }

    // Enable/disable auto-save
    void SetAutoSaveEnabled(bool enabled) { auto_save_enabled_ = enabled; }

    // Mark config as dirty (needs saving)
    void MarkDirty() { config_dirty_ = true; }

    // Check if config is dirty
    bool IsDirty() const { return config_dirty_; }

    // Reset to default configuration
    void ResetToDefaults();

    // Get default config file path based on platform
    static std::string GetDefaultConfigPath();

private:
    AppConfig config_;
    std::string config_file_path_;
    bool auto_save_enabled_;
    bool config_dirty_;

    // Helper functions for JSON serialization
    bool LoadFromJSON(const std::string& json_content);
    std::string SaveToJSON() const;

    // Platform-specific config directory functions
    static std::string GetConfigDirectory();
    static std::string GetExecutableDirectory();
    
    // File I/O helpers
    bool ReadFile(const std::string& file_path, std::string& content);
    bool WriteFile(const std::string& file_path, const std::string& content);
    bool CreateDirectoryIfNotExists(const std::string& dir_path);
};
